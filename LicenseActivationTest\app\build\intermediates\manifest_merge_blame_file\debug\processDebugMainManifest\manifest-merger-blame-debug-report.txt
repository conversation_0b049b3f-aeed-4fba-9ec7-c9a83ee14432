1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.appystore.mrecharge"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-feature
11-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:5:5-7:36
12        android:name="android.hardware.telephony"
12-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:6:9-50
13        android:required="false" />
13-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:7:9-33
14
15    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
15-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:9:5-80
15-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:9:22-78
16    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
16-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:10:5-78
16-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:10:22-76
17    <uses-permission android:name="android.permission.INTERNET" />
17-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:11:5-66
17-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:11:22-64
18    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
18-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:12:5-75
18-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:12:22-73
19    <uses-permission android:name="android.permission.READ_SMS" />
19-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:13:5-66
19-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:13:22-64
20    <uses-permission android:name="android.permission.SEND_SMS" />
20-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:14:5-66
20-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:14:22-64
21    <uses-permission android:name="android.permission.RECEIVE_SMS" />
21-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:15:5-69
21-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:15:22-67
22    <uses-permission android:name="android.permission.CALL_PHONE" />
22-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:16:5-68
22-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:16:22-66
23    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
23-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:17:5-74
23-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:17:22-72
24    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
24-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:18:5-76
24-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:18:22-74
25    <uses-permission android:name="android.permission.WAKE_LOCK" />
25-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:19:5-67
25-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:19:22-65
26    <uses-permission android:name="android.permission.DISABLE_KEYGUARD" />
26-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:20:5-74
26-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:20:22-72
27    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
27-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:21:5-94
27-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:21:22-92
28    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
28-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:22:5-77
28-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:22:22-75
29    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
29-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:23:5-81
29-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:23:22-79
30    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
30-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:24:5-109
30-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:24:22-107
31    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
31-->[com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9b6d5929ca286945b5ef616f64e4ea48\transformed\firebase-messaging-24.0.0\AndroidManifest.xml:23:5-77
31-->[com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9b6d5929ca286945b5ef616f64e4ea48\transformed\firebase-messaging-24.0.0\AndroidManifest.xml:23:22-74
32    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
32-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\435d61ecbbe3481154f76d1f2bd8d528\transformed\recaptcha-18.6.1\AndroidManifest.xml:9:5-98
32-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\435d61ecbbe3481154f76d1f2bd8d528\transformed\recaptcha-18.6.1\AndroidManifest.xml:9:22-95
33
34    <permission
34-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\534e5608d8b8e741317680d9a7d159af\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
35        android:name="com.appystore.mrecharge.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
35-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\534e5608d8b8e741317680d9a7d159af\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
36        android:protectionLevel="signature" />
36-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\534e5608d8b8e741317680d9a7d159af\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
37
38    <uses-permission android:name="com.appystore.mrecharge.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
38-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\534e5608d8b8e741317680d9a7d159af\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
38-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\534e5608d8b8e741317680d9a7d159af\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
39
40    <application
40-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:26:5-308:19
41        android:allowBackup="true"
41-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:27:9-35
42        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
42-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\534e5608d8b8e741317680d9a7d159af\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
43        android:dataExtractionRules="@xml/data_extraction_rules"
43-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:28:9-65
44        android:debuggable="true"
45        android:extractNativeLibs="false"
46        android:fullBackupContent="@xml/backup_rules"
46-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:29:9-54
47        android:icon="@mipmap/ic_launcher"
47-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:30:9-43
48        android:label="@string/app_name"
48-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:31:9-41
49        android:networkSecurityConfig="@xml/network_security_config"
49-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:35:9-69
50        android:roundIcon="@mipmap/ic_launcher"
50-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:32:9-48
51        android:supportsRtl="true"
51-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:33:9-35
52        android:testOnly="true"
53        android:theme="@style/Theme.AppyStoreMRecharge"
53-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:34:9-56
54        android:usesCleartextTraffic="true" >
54-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:36:9-44
55        <uses-library
55-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:38:9-40:39
56            android:name="org.apache.http.legacy"
56-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:39:13-50
57            android:required="false" />
57-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:40:13-37
58
59        <activity
59-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:41:9-49:20
60            android:name="com.appystore.mrecharge.activity.MainActivity"
60-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:44:13-73
61            android:exported="true"
61-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:42:13-36
62            android:label="@string/app_name" >
62-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:43:13-45
63            <intent-filter>
63-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:45:13-48:29
64                <action android:name="android.intent.action.MAIN" />
64-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:46:17-68
64-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:46:25-66
65
66                <category android:name="android.intent.category.LAUNCHER" />
66-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:47:17-76
66-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:47:27-74
67            </intent-filter>
68        </activity>
69
70        <receiver
70-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:50:9-55:20
71            android:name="com.appystore.mrecharge.IncomingSms"
71-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:50:19-69
72            android:exported="true" >
72-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:51:13-36
73            <intent-filter android:priority="1000" >
73-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:52:13-54:29
73-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:52:28-51
74                <action android:name="android.provider.Telephony.SMS_RECEIVED" />
74-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:53:17-81
74-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:53:25-79
75            </intent-filter>
76        </receiver>
77
78        <service
78-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:56:9-61:19
79            android:name="com.appystore.mrecharge.service.PushService"
79-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:56:18-76
80            android:exported="false" >
80-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:57:13-37
81            <intent-filter>
81-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:58:13-60:29
82                <action android:name="com.google.firebase.MESSAGING_EVENT" />
82-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:59:17-77
82-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:59:25-75
83            </intent-filter>
84        </service>
85        <service android:name="com.appystore.mrecharge.service.Recharge" />
85-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:62:9-75
85-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:62:18-73
86        <service
86-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:63:9-67:43
87            android:name="com.appystore.mrecharge.service.sever"
87-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:64:13-65
88            android:enabled="true"
88-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:65:13-35
89            android:exported="true"
89-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:66:13-36
90            android:stopWithTask="false" />
90-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:67:13-41
91        <service android:name="com.appystore.mrecharge.service.Smsend" />
91-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:68:9-73
91-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:68:18-71
92        <service android:name="com.appystore.mrecharge.service.International_service" />
92-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:69:9-88
92-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:69:18-86
93        <service
93-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:70:9-80:19
94            android:name="com.appystore.mrecharge.service.USSDService"
94-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:71:13-71
95            android:exported="true"
95-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:73:13-36
96            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE" >
96-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:72:13-79
97            <intent-filter>
97-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:74:13-76:29
98                <action android:name="android.accessibilityservice.AccessibilityService" />
98-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:75:17-91
98-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:75:25-89
99            </intent-filter>
100
101            <meta-data
101-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:77:13-79:55
102                android:name="android.accessibilityservice"
102-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:78:17-60
103                android:resource="@xml/ussd_service" />
103-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:79:17-53
104        </service>
105
106        <activity
106-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:81:9-102
107            android:name="com.appystore.mrecharge.activity.Settings"
107-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:81:19-75
108            android:exported="false" />
108-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:81:76-100
109        <activity
109-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:82:9-104
110            android:name="com.appystore.mrecharge.activity.intsetting"
110-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:82:19-77
111            android:exported="false" />
111-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:82:78-102
112        <activity
112-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:83:9-104
113            android:name="com.appystore.mrecharge.activity.Monitoring"
113-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:83:19-77
114            android:exported="false" />
114-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:83:78-102
115
116        <provider
116-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:84:9-91:20
117            android:name="androidx.startup.InitializationProvider"
117-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:85:13-67
118            android:authorities="com.appystore.mrecharge.androidx-startup"
118-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:87:13-75
119            android:exported="false" >
119-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:86:13-37
120            <meta-data
120-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:88:13-90:51
121                android:name="androidx.work.WorkManagerInitializer"
121-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:89:17-68
122                android:value="androidx.startup" />
122-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:90:17-49
123            <meta-data
123-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\aa38a909f3c22e0015a23f181d9b4b3b\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
124                android:name="androidx.emoji2.text.EmojiCompatInitializer"
124-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\aa38a909f3c22e0015a23f181d9b4b3b\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
125                android:value="androidx.startup" />
125-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\aa38a909f3c22e0015a23f181d9b4b3b\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
126            <meta-data
126-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\a8f1fc009573b7426ba9e8f3843153ff\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
127                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
127-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\a8f1fc009573b7426ba9e8f3843153ff\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
128                android:value="androidx.startup" />
128-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\a8f1fc009573b7426ba9e8f3843153ff\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
129            <meta-data
129-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2044b426090b747abc60e59a67ed720f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
130                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
130-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2044b426090b747abc60e59a67ed720f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
131                android:value="androidx.startup" />
131-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2044b426090b747abc60e59a67ed720f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
132        </provider>
133
134        <service
134-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:92:9-96:46
135            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
135-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:93:13-88
136            android:directBootAware="false"
136-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:96:13-44
137            android:enabled="@bool/enable_system_alarm_service_default"
137-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:94:13-72
138            android:exported="false" />
138-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:95:13-37
139        <service
139-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:97:9-102:46
140            android:name="androidx.work.impl.background.systemjob.SystemJobService"
140-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:98:13-84
141            android:directBootAware="false"
141-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:102:13-44
142            android:enabled="@bool/enable_system_job_service_default"
142-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:100:13-70
143            android:exported="true"
143-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:101:13-36
144            android:permission="android.permission.BIND_JOB_SERVICE" />
144-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:99:13-69
145        <service
145-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:103:9-107:46
146            android:name="androidx.work.impl.foreground.SystemForegroundService"
146-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:104:13-81
147            android:directBootAware="false"
147-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:107:13-44
148            android:enabled="@bool/enable_system_foreground_service_default"
148-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:105:13-77
149            android:exported="false" />
149-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:106:13-37
150
151        <receiver
151-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:108:9-112:46
152            android:name="androidx.work.impl.utils.ForceStopRunnable.BroadcastReceiver"
152-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:109:13-88
153            android:directBootAware="false"
153-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:112:13-44
154            android:enabled="true"
154-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:110:13-35
155            android:exported="false" />
155-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:111:13-37
156        <receiver
156-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:113:9-122:20
157            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy.BatteryChargingProxy"
157-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:114:13-106
158            android:directBootAware="false"
158-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:117:13-44
159            android:enabled="false"
159-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:115:13-36
160            android:exported="false" >
160-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:116:13-37
161            <intent-filter>
161-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:118:13-121:29
162                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
162-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:119:17-86
162-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:119:25-84
163                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
163-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:120:17-89
163-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:120:25-87
164            </intent-filter>
165        </receiver>
166        <receiver
166-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:123:9-132:20
167            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy.BatteryNotLowProxy"
167-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:124:13-104
168            android:directBootAware="false"
168-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:127:13-44
169            android:enabled="false"
169-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:125:13-36
170            android:exported="false" >
170-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:126:13-37
171            <intent-filter>
171-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:128:13-131:29
172                <action android:name="android.intent.action.BATTERY_OKAY" />
172-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:129:17-76
172-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:129:25-74
173                <action android:name="android.intent.action.BATTERY_LOW" />
173-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:130:17-75
173-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:130:25-73
174            </intent-filter>
175        </receiver>
176        <receiver
176-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:133:9-142:20
177            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy.StorageNotLowProxy"
177-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:134:13-104
178            android:directBootAware="false"
178-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:137:13-44
179            android:enabled="false"
179-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:135:13-36
180            android:exported="false" >
180-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:136:13-37
181            <intent-filter>
181-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:138:13-141:29
182                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
182-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:139:17-82
182-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:139:25-80
183                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
183-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:140:17-81
183-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:140:25-79
184            </intent-filter>
185        </receiver>
186        <receiver
186-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:143:9-151:20
187            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy.NetworkStateProxy"
187-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:144:13-103
188            android:directBootAware="false"
188-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:147:13-44
189            android:enabled="false"
189-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:145:13-36
190            android:exported="false" >
190-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:146:13-37
191            <intent-filter>
191-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:148:13-150:29
192                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
192-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:149:17-78
192-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:149:25-76
193            </intent-filter>
194        </receiver>
195        <receiver
195-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:152:9-162:20
196            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
196-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:153:13-88
197            android:directBootAware="false"
197-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:156:13-44
198            android:enabled="false"
198-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:154:13-36
199            android:exported="false" >
199-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:155:13-37
200            <intent-filter>
200-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:157:13-161:29
201                <action android:name="android.intent.action.BOOT_COMPLETED" />
201-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:158:17-78
201-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:158:25-76
202                <action android:name="android.intent.action.TIME_SET" />
202-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:159:17-72
202-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:159:25-70
203                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
203-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:160:17-80
203-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:160:25-78
204            </intent-filter>
205        </receiver>
206        <receiver
206-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:163:9-171:20
207            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
207-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:164:13-99
208            android:directBootAware="false"
208-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:167:13-44
209            android:enabled="@bool/enable_system_alarm_service_default"
209-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:165:13-72
210            android:exported="false" >
210-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:166:13-37
211            <intent-filter>
211-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:168:13-170:29
212                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
212-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:169:17-97
212-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:169:25-95
213            </intent-filter>
214        </receiver>
215        <receiver
215-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:172:9-181:20
216            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
216-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:173:13-78
217            android:directBootAware="false"
217-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:177:13-44
218            android:enabled="true"
218-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:175:13-35
219            android:exported="true"
219-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:176:13-36
220            android:permission="android.permission.DUMP" >
220-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:174:13-57
221            <intent-filter>
221-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:178:13-180:29
222                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
222-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:179:17-87
222-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:179:25-85
223            </intent-filter>
224        </receiver>
225
226        <activity
226-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:182:9-197:20
227            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
227-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:184:13-80
228            android:excludeFromRecents="true"
228-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:186:13-46
229            android:exported="true"
229-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:185:13-36
230            android:launchMode="singleTask"
230-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:187:13-44
231            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
231-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:183:13-72
232            <intent-filter>
232-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:188:13-196:29
233                <action android:name="android.intent.action.VIEW" />
233-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:189:17-68
233-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:189:25-66
234
235                <category android:name="android.intent.category.DEFAULT" />
235-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:190:17-75
235-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:190:27-73
236                <category android:name="android.intent.category.BROWSABLE" />
236-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:191:17-77
236-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:191:27-75
237
238                <data
238-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:192:17-195:39
239                    android:host="firebase.auth"
239-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:194:21-49
240                    android:path="/"
240-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:195:21-37
241                    android:scheme="genericidp" />
241-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:193:21-48
242            </intent-filter>
243        </activity>
244        <activity
244-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:198:9-213:20
245            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
245-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:200:13-79
246            android:excludeFromRecents="true"
246-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:202:13-46
247            android:exported="true"
247-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:201:13-36
248            android:launchMode="singleTask"
248-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:203:13-44
249            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
249-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:199:13-72
250            <intent-filter>
250-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:204:13-212:29
251                <action android:name="android.intent.action.VIEW" />
251-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:189:17-68
251-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:189:25-66
252
253                <category android:name="android.intent.category.DEFAULT" />
253-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:190:17-75
253-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:190:27-73
254                <category android:name="android.intent.category.BROWSABLE" />
254-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:191:17-77
254-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:191:27-75
255
256                <data
256-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:192:17-195:39
257                    android:host="firebase.auth"
257-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:194:21-49
258                    android:path="/"
258-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:195:21-37
259                    android:scheme="recaptcha" />
259-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:193:21-48
260            </intent-filter>
261        </activity>
262
263        <service
263-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:214:9-222:19
264            android:name="com.google.firebase.auth.api.fallback.service.FirebaseAuthFallbackService"
264-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:215:13-101
265            android:enabled="true"
265-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:216:13-35
266            android:exported="false" >
266-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:217:13-37
267            <intent-filter>
267-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:218:13-221:29
268                <action android:name="com.google.firebase.auth.api.gms.service.START" />
268-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:219:17-88
268-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:219:25-86
269
270                <category android:name="android.intent.category.DEFAULT" />
270-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:190:17-75
270-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:190:27-73
271            </intent-filter>
272        </service>
273        <service
273-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:223:9-245:19
274            android:name="com.google.firebase.components.ComponentDiscoveryService"
274-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:224:13-84
275            android:directBootAware="true"
275-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:226:13-43
276            android:exported="false" >
276-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:225:13-37
277            <meta-data
277-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:227:13-229:84
278                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
278-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:228:17-109
279                android:value="com.google.firebase.components.ComponentRegistrar" />
279-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:229:17-82
280            <meta-data
280-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:230:13-232:84
281                android:name="com.google.firebase.components:com.google.firebase.crashlytics.CrashlyticsRegistrar"
281-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:231:17-115
282                android:value="com.google.firebase.components.ComponentRegistrar" />
282-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:232:17-82
283            <meta-data
283-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:233:13-235:84
284                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
284-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:234:17-119
285                android:value="com.google.firebase.components.ComponentRegistrar" />
285-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:235:17-82
286            <meta-data
286-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:236:13-238:84
287                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
287-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:237:17-115
288                android:value="com.google.firebase.components.ComponentRegistrar" />
288-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:238:17-82
289            <meta-data
289-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:239:13-241:84
290                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
290-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:240:17-139
291                android:value="com.google.firebase.components.ComponentRegistrar" />
291-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:241:17-82
292            <meta-data
292-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:242:13-244:84
293                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
293-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:243:17-127
294                android:value="com.google.firebase.components.ComponentRegistrar" />
294-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:244:17-82
295            <meta-data
295-->[com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9b6d5929ca286945b5ef616f64e4ea48\transformed\firebase-messaging-24.0.0\AndroidManifest.xml:57:13-59:85
296                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
296-->[com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9b6d5929ca286945b5ef616f64e4ea48\transformed\firebase-messaging-24.0.0\AndroidManifest.xml:58:17-122
297                android:value="com.google.firebase.components.ComponentRegistrar" />
297-->[com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9b6d5929ca286945b5ef616f64e4ea48\transformed\firebase-messaging-24.0.0\AndroidManifest.xml:59:17-82
298            <meta-data
298-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\501be6f5311fc7674eaab5b094c8d259\transformed\firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
299                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
299-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\501be6f5311fc7674eaab5b094c8d259\transformed\firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
300                android:value="com.google.firebase.components.ComponentRegistrar" />
300-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\501be6f5311fc7674eaab5b094c8d259\transformed\firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
301            <meta-data
301-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\a2ee6c6ec182e85729b567b0981fb038\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
302                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
302-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\a2ee6c6ec182e85729b567b0981fb038\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
303                android:value="com.google.firebase.components.ComponentRegistrar" />
303-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\a2ee6c6ec182e85729b567b0981fb038\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
304            <meta-data
304-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\07f678a0ca4907834db1d485ccc0617e\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
305                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
305-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\07f678a0ca4907834db1d485ccc0617e\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
306                android:value="com.google.firebase.components.ComponentRegistrar" />
306-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\07f678a0ca4907834db1d485ccc0617e\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
307        </service>
308
309        <receiver
309-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:246:9-253:20
310            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
310-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:247:13-78
311            android:exported="true"
311-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:249:13-36
312            android:permission="com.google.android.c2dm.permission.SEND" >
312-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:248:13-73
313            <intent-filter>
313-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:250:13-252:29
314                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
314-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:251:17-80
314-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:251:25-78
315            </intent-filter>
316
317            <meta-data
317-->[com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9b6d5929ca286945b5ef616f64e4ea48\transformed\firebase-messaging-24.0.0\AndroidManifest.xml:37:13-39:40
318                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
318-->[com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9b6d5929ca286945b5ef616f64e4ea48\transformed\firebase-messaging-24.0.0\AndroidManifest.xml:38:17-92
319                android:value="true" />
319-->[com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9b6d5929ca286945b5ef616f64e4ea48\transformed\firebase-messaging-24.0.0\AndroidManifest.xml:39:17-37
320        </receiver>
321
322        <service
322-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:254:9-261:19
323            android:name="com.google.firebase.messaging.FirebaseMessagingService"
323-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:255:13-82
324            android:directBootAware="true"
324-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:257:13-43
325            android:exported="false" >
325-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:256:13-37
326            <intent-filter android:priority="-500" >
326-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:58:13-60:29
326-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:258:28-51
327                <action android:name="com.google.firebase.MESSAGING_EVENT" />
327-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:59:17-77
327-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:59:25-75
328            </intent-filter>
329        </service>
330        <service
330-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:262:9-268:19
331            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
331-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:263:13-103
332            android:exported="false" >
332-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:264:13-37
333            <meta-data
333-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:265:13-267:38
334                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
334-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:266:17-94
335                android:value="cct" />
335-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:267:17-36
336        </service>
337
338        <provider
338-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:269:9-274:45
339            android:name="com.google.firebase.provider.FirebaseInitProvider"
339-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:270:13-77
340            android:authorities="com.appystore.mrecharge.firebaseinitprovider"
340-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:272:13-79
341            android:directBootAware="true"
341-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:274:13-43
342            android:exported="false"
342-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:271:13-37
343            android:initOrder="100" />
343-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:273:13-36
344
345        <activity
345-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:275:9-278:39
346            android:name="com.google.android.gms.common.api.GoogleApiActivity"
346-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:277:13-79
347            android:exported="false"
347-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:278:13-37
348            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
348-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:276:13-72
349
350        <receiver
350-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:279:9-282:39
351            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
351-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:280:13-85
352            android:enabled="true"
352-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:281:13-35
353            android:exported="false" />
353-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:282:13-37
354
355        <service
355-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:283:9-286:39
356            android:name="com.google.android.gms.measurement.AppMeasurementService"
356-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:284:13-84
357            android:enabled="true"
357-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:285:13-35
358            android:exported="false" />
358-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:286:13-37
359        <service
359-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:287:9-291:39
360            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
360-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:288:13-87
361            android:enabled="true"
361-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:290:13-35
362            android:exported="false"
362-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:291:13-37
363            android:permission="android.permission.BIND_JOB_SERVICE" />
363-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:289:13-69
364
365        <meta-data
365-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:292:9-294:68
366            android:name="com.google.android.gms.version"
366-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:293:13-58
367            android:value="@integer/google_play_services_version" />
367-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:294:13-66
368
369        <service
369-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:295:9-298:45
370            android:name="androidx.room.MultiInstanceInvalidationService"
370-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:296:13-74
371            android:directBootAware="true"
371-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:298:13-43
372            android:exported="false" />
372-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:297:13-37
373        <service
373-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:299:9-302:39
374            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
374-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:300:13-117
375            android:exported="false"
375-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:302:13-37
376            android:permission="android.permission.BIND_JOB_SERVICE" />
376-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:301:13-69
377
378        <receiver
378-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:303:9-305:39
379            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
379-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:304:13-132
380            android:exported="false" />
380-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:305:13-37
381
382        <service
382-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14\transforms\196accc71b87476783482adbbea29fca\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:24:9-32:19
383            android:name="androidx.credentials.playservices.CredentialProviderMetadataHolder"
383-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14\transforms\196accc71b87476783482adbbea29fca\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:25:13-94
384            android:enabled="true"
384-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14\transforms\196accc71b87476783482adbbea29fca\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:26:13-35
385            android:exported="false" >
385-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14\transforms\196accc71b87476783482adbbea29fca\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:27:13-37
386            <meta-data
386-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14\transforms\196accc71b87476783482adbbea29fca\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:29:13-31:104
387                android:name="androidx.credentials.CREDENTIAL_PROVIDER_KEY"
387-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14\transforms\196accc71b87476783482adbbea29fca\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:30:17-76
388                android:value="androidx.credentials.playservices.CredentialProviderPlayServicesImpl" />
388-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14\transforms\196accc71b87476783482adbbea29fca\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:31:17-101
389        </service>
390
391        <activity
391-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14\transforms\196accc71b87476783482adbbea29fca\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:34:9-41:20
392            android:name="androidx.credentials.playservices.HiddenActivity"
392-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14\transforms\196accc71b87476783482adbbea29fca\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:35:13-76
393            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
393-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14\transforms\196accc71b87476783482adbbea29fca\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:36:13-87
394            android:enabled="true"
394-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14\transforms\196accc71b87476783482adbbea29fca\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:37:13-35
395            android:exported="false"
395-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14\transforms\196accc71b87476783482adbbea29fca\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:38:13-37
396            android:fitsSystemWindows="true"
396-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14\transforms\196accc71b87476783482adbbea29fca\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:39:13-45
397            android:theme="@style/Theme.Hidden" >
397-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14\transforms\196accc71b87476783482adbbea29fca\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:40:13-48
398        </activity>
399        <activity
399-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9dd3b1cd2b8985ff1cd94450d80c19b1\transformed\play-services-auth-20.7.0\AndroidManifest.xml:23:9-27:75
400            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
400-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9dd3b1cd2b8985ff1cd94450d80c19b1\transformed\play-services-auth-20.7.0\AndroidManifest.xml:24:13-93
401            android:excludeFromRecents="true"
401-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9dd3b1cd2b8985ff1cd94450d80c19b1\transformed\play-services-auth-20.7.0\AndroidManifest.xml:25:13-46
402            android:exported="false"
402-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9dd3b1cd2b8985ff1cd94450d80c19b1\transformed\play-services-auth-20.7.0\AndroidManifest.xml:26:13-37
403            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
403-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9dd3b1cd2b8985ff1cd94450d80c19b1\transformed\play-services-auth-20.7.0\AndroidManifest.xml:27:13-72
404        <!--
405            Service handling Google Sign-In user revocation. For apps that do not integrate with
406            Google Sign-In, this service will never be started.
407        -->
408        <service
408-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9dd3b1cd2b8985ff1cd94450d80c19b1\transformed\play-services-auth-20.7.0\AndroidManifest.xml:33:9-37:51
409            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
409-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9dd3b1cd2b8985ff1cd94450d80c19b1\transformed\play-services-auth-20.7.0\AndroidManifest.xml:34:13-89
410            android:exported="true"
410-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9dd3b1cd2b8985ff1cd94450d80c19b1\transformed\play-services-auth-20.7.0\AndroidManifest.xml:35:13-36
411            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
411-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9dd3b1cd2b8985ff1cd94450d80c19b1\transformed\play-services-auth-20.7.0\AndroidManifest.xml:36:13-107
412            android:visibleToInstantApps="true" />
412-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9dd3b1cd2b8985ff1cd94450d80c19b1\transformed\play-services-auth-20.7.0\AndroidManifest.xml:37:13-48
413
414        <receiver
414-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2346c276e8123d58131e2bf72ee70526\transformed\work-runtime-2.9.0\AndroidManifest.xml:61:9-66:35
415            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
415-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2346c276e8123d58131e2bf72ee70526\transformed\work-runtime-2.9.0\AndroidManifest.xml:62:13-88
416            android:directBootAware="false"
416-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2346c276e8123d58131e2bf72ee70526\transformed\work-runtime-2.9.0\AndroidManifest.xml:63:13-44
417            android:enabled="true"
417-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2346c276e8123d58131e2bf72ee70526\transformed\work-runtime-2.9.0\AndroidManifest.xml:64:13-35
418            android:exported="false" />
418-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2346c276e8123d58131e2bf72ee70526\transformed\work-runtime-2.9.0\AndroidManifest.xml:65:13-37
419        <receiver
419-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2346c276e8123d58131e2bf72ee70526\transformed\work-runtime-2.9.0\AndroidManifest.xml:67:9-77:20
420            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
420-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2346c276e8123d58131e2bf72ee70526\transformed\work-runtime-2.9.0\AndroidManifest.xml:68:13-106
421            android:directBootAware="false"
421-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2346c276e8123d58131e2bf72ee70526\transformed\work-runtime-2.9.0\AndroidManifest.xml:69:13-44
422            android:enabled="false"
422-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2346c276e8123d58131e2bf72ee70526\transformed\work-runtime-2.9.0\AndroidManifest.xml:70:13-36
423            android:exported="false" >
423-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2346c276e8123d58131e2bf72ee70526\transformed\work-runtime-2.9.0\AndroidManifest.xml:71:13-37
424            <intent-filter>
424-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:118:13-121:29
425                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
425-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:119:17-86
425-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:119:25-84
426                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
426-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:120:17-89
426-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:120:25-87
427            </intent-filter>
428        </receiver>
429        <receiver
429-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2346c276e8123d58131e2bf72ee70526\transformed\work-runtime-2.9.0\AndroidManifest.xml:78:9-88:20
430            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
430-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2346c276e8123d58131e2bf72ee70526\transformed\work-runtime-2.9.0\AndroidManifest.xml:79:13-104
431            android:directBootAware="false"
431-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2346c276e8123d58131e2bf72ee70526\transformed\work-runtime-2.9.0\AndroidManifest.xml:80:13-44
432            android:enabled="false"
432-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2346c276e8123d58131e2bf72ee70526\transformed\work-runtime-2.9.0\AndroidManifest.xml:81:13-36
433            android:exported="false" >
433-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2346c276e8123d58131e2bf72ee70526\transformed\work-runtime-2.9.0\AndroidManifest.xml:82:13-37
434            <intent-filter>
434-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:128:13-131:29
435                <action android:name="android.intent.action.BATTERY_OKAY" />
435-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:129:17-76
435-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:129:25-74
436                <action android:name="android.intent.action.BATTERY_LOW" />
436-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:130:17-75
436-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:130:25-73
437            </intent-filter>
438        </receiver>
439        <receiver
439-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2346c276e8123d58131e2bf72ee70526\transformed\work-runtime-2.9.0\AndroidManifest.xml:89:9-99:20
440            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
440-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2346c276e8123d58131e2bf72ee70526\transformed\work-runtime-2.9.0\AndroidManifest.xml:90:13-104
441            android:directBootAware="false"
441-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2346c276e8123d58131e2bf72ee70526\transformed\work-runtime-2.9.0\AndroidManifest.xml:91:13-44
442            android:enabled="false"
442-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2346c276e8123d58131e2bf72ee70526\transformed\work-runtime-2.9.0\AndroidManifest.xml:92:13-36
443            android:exported="false" >
443-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2346c276e8123d58131e2bf72ee70526\transformed\work-runtime-2.9.0\AndroidManifest.xml:93:13-37
444            <intent-filter>
444-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:138:13-141:29
445                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
445-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:139:17-82
445-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:139:25-80
446                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
446-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:140:17-81
446-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:140:25-79
447            </intent-filter>
448        </receiver>
449        <receiver
449-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2346c276e8123d58131e2bf72ee70526\transformed\work-runtime-2.9.0\AndroidManifest.xml:100:9-109:20
450            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
450-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2346c276e8123d58131e2bf72ee70526\transformed\work-runtime-2.9.0\AndroidManifest.xml:101:13-103
451            android:directBootAware="false"
451-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2346c276e8123d58131e2bf72ee70526\transformed\work-runtime-2.9.0\AndroidManifest.xml:102:13-44
452            android:enabled="false"
452-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2346c276e8123d58131e2bf72ee70526\transformed\work-runtime-2.9.0\AndroidManifest.xml:103:13-36
453            android:exported="false" >
453-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2346c276e8123d58131e2bf72ee70526\transformed\work-runtime-2.9.0\AndroidManifest.xml:104:13-37
454            <intent-filter>
454-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:148:13-150:29
455                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
455-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:149:17-78
455-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:149:25-76
456            </intent-filter>
457        </receiver>
458        <receiver
458-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2044b426090b747abc60e59a67ed720f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
459            android:name="androidx.profileinstaller.ProfileInstallReceiver"
459-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2044b426090b747abc60e59a67ed720f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
460            android:directBootAware="false"
460-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2044b426090b747abc60e59a67ed720f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
461            android:enabled="true"
461-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2044b426090b747abc60e59a67ed720f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
462            android:exported="true"
462-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2044b426090b747abc60e59a67ed720f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
463            android:permission="android.permission.DUMP" >
463-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2044b426090b747abc60e59a67ed720f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
464            <intent-filter>
464-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2044b426090b747abc60e59a67ed720f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
465                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
465-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2044b426090b747abc60e59a67ed720f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
465-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2044b426090b747abc60e59a67ed720f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
466            </intent-filter>
467            <intent-filter>
467-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2044b426090b747abc60e59a67ed720f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
468                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
468-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2044b426090b747abc60e59a67ed720f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
468-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2044b426090b747abc60e59a67ed720f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
469            </intent-filter>
470            <intent-filter>
470-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2044b426090b747abc60e59a67ed720f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
471                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
471-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2044b426090b747abc60e59a67ed720f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
471-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2044b426090b747abc60e59a67ed720f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
472            </intent-filter>
473            <intent-filter>
473-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2044b426090b747abc60e59a67ed720f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
474                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
474-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2044b426090b747abc60e59a67ed720f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
474-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2044b426090b747abc60e59a67ed720f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
475            </intent-filter>
476        </receiver> <!-- The activities will be merged into the manifest of the hosting app. -->
477        <activity
477-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\27726287844e0c57051862f89d0a94a5\transformed\core-common-2.0.3\AndroidManifest.xml:14:9-18:65
478            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
478-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\27726287844e0c57051862f89d0a94a5\transformed\core-common-2.0.3\AndroidManifest.xml:15:13-93
479            android:exported="false"
479-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\27726287844e0c57051862f89d0a94a5\transformed\core-common-2.0.3\AndroidManifest.xml:16:13-37
480            android:stateNotNeeded="true"
480-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\27726287844e0c57051862f89d0a94a5\transformed\core-common-2.0.3\AndroidManifest.xml:17:13-42
481            android:theme="@style/Theme.PlayCore.Transparent" />
481-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\27726287844e0c57051862f89d0a94a5\transformed\core-common-2.0.3\AndroidManifest.xml:18:13-62
482    </application>
483
484</manifest>
